import { Calendar, Circle } from 'lucide-react';
import { Link, useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { NewsItem } from '../types';
import { createImageLoader } from '../../../services/imageService';

export function NewsCard({
  id,
  title,
  summary,
  date,
  imageUrl,
  tags,
  themes_cn,
  isRead = true,
  rocketName,
  originalData
}: NewsItem & { rocketName: string }) {
  // 使用themes_cn字段，如果没有则使用tags字段
  const displayTags = themes_cn || tags || [];
  const [searchParams] = useSearchParams();
  const currentType = searchParams.get('type') || 'rocket';

  // 创建图片加载器，优先使用jpg_path，然后是imageUrl
  const { imageUrl: finalImageUrl, onError } = createImageLoader(
    originalData?.jpg_path || imageUrl
  );

  return (
    <motion.article
      whileHover={{ y: -2 }}
      className="
        bg-white/5 hover:bg-white/[0.07]
        backdrop-blur-sm
        border border-white/10
        rounded-xl
        overflow-hidden
        group
        transition-all duration-200
        relative
      "
    >
      {/* 未读标签 */}
      {!isRead && (
        <div className="absolute top-2 right-2 z-10 flex items-center gap-1.5 px-2 py-1 rounded-full bg-blue-500/20 shadow-md">
          <Circle className="w-2 h-2 fill-blue-400 text-blue-400" />
          <span className="text-xs text-blue-400">未读</span>
        </div>
      )}

      <Link to={`/news/${id}?from=${currentType}&rocketName=${encodeURIComponent(rocketName)}`} className="block">
        <div className="flex gap-6 p-6">
          {/* Image - 使用FTP代理服务获取图片 */}
          <div className="w-48 h-32 flex-shrink-0 overflow-hidden rounded-lg">
            <img
              src={finalImageUrl}
              alt={title}
              onError={onError}
              className="
                w-full h-full object-cover
                transform group-hover:scale-105
                transition-transform duration-300
              "
            />
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-medium text-white mb-3 line-clamp-2 group-hover:text-blue-400 transition-colors">
              {title}
            </h3>

            <p className="text-sm text-gray-300 mb-4 line-clamp-3">
              {summary}
            </p>

            <div className="flex flex-wrap items-center gap-4">
              {/* Tags */}
              {displayTags.length > 0 && (
                <div className="flex flex-wrap items-center gap-2">
                  {displayTags.map((tag) => (
                    <span
                      key={tag}
                      className="px-2 py-1 text-xs bg-white/5 text-white/70 rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}

              {/* Date */}
              <div className="flex items-center gap-2 text-sm text-gray-400">
                <Calendar className="w-4 h-4" />
                <span>{date}</span>
              </div>
            </div>
          </div>
        </div>
      </Link>
    </motion.article>
  );
}