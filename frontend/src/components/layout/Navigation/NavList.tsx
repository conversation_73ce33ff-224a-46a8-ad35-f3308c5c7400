import React from 'react';
import { NavItem } from './NavItem';
import { navItems } from '../../../data/navigation';
import { useAuthStore } from '../../../store/auth';

export function NavList({ isMobile = false }) {
  const { userInfo } = useAuthStore();

  // 根据用户权限过滤导航项
  const filteredNavItems = navItems.filter(item => {
    // 如果导航项没有权限要求，则所有用户都可以看到
    if (!item.requiredRole) {
      return true;
    }
    
    // 如果用户未登录，则隐藏需要权限的导航项
    if (!userInfo) {
      return false;
    }
    
    // 检查用户是否具有所需角色
    return userInfo.role === item.requiredRole;
  });

  // 需要在其后添加分割线的导航项（根据标签名判断）
  const shouldAddDividerAfter = (label: string) => {
    return ['首页', '搜索', '新闻', '发射'].includes(label);
  };

  return (
    <div className={`
      ${isMobile ? 'flex-col w-full space-y-1' : 'flex items-center h-14 space-x-1'}
    `}>
      {filteredNavItems.map((item, index) => (
        <React.Fragment key={item.label}>
          <NavItem 
            {...item} 
            isMobile={isMobile}
          />
          {/* 移动端添加分割线 */}
          {isMobile && shouldAddDividerAfter(item.label) && index < filteredNavItems.length - 1 && (
            <div className="mx-4 my-3">
              <div className="h-px bg-gradient-to-r from-transparent via-white/30 to-transparent" />
            </div>
          )}
        </React.Fragment>
      ))}
    </div>
  );
}