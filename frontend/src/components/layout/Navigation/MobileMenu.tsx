import React, { forwardRef } from 'react';
import { useSwipeable } from '../../../hooks/useSwipeable';

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

export const MobileMenu = forwardRef<HTMLDivElement, MobileMenuProps>(
  function MobileMenu({ isOpen, onClose, children }, ref) {
    const handlers = useSwipeable({
      onSwipeLeft: onClose,
      threshold: 50,
    });

    return (
      <div 
        className={`
          fixed inset-0 z-40
          transition-all duration-300 ease-in-out
          ${isOpen ? 'visible opacity-100' : 'invisible opacity-0 pointer-events-none'}
        `}
        aria-hidden={!isOpen}
      >
        {/* Backdrop */}
        <div 
          onClick={onClose}
          className={`
            absolute inset-0
            bg-black/40 backdrop-blur-sm
            transition-opacity duration-300 cursor-pointer
            ${isOpen ? 'opacity-100' : 'opacity-0'}
          `}
        />

        {/* Menu Panel */}
        <div 
          ref={ref}
          {...handlers}
          className={`
            absolute left-0 top-0 bottom-0 
            w-72
            transition-transform duration-300 ease-out
            ${isOpen ? 'translate-x-0' : '-translate-x-full'}
          `}
          onClick={(e) => e.stopPropagation()}
        >
          {/* 内部卡片容器 */}
          <div className="h-full bg-white/10 backdrop-blur-xl border-r border-white/20 shadow-2xl">
            {/* 菜单头部 */}
            <div className="px-6 py-6 bg-gradient-to-r from-blue-600/20 to-purple-600/20 border-b border-white/20">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
                    <svg className="w-6 h-6 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-white">太空数据平台</h3>
                  <p className="text-sm text-white/70">快速导航</p>
                </div>
              </div>
            </div>
            
            {/* 菜单内容 */}
            <div className="py-4 px-2">
              {children}
            </div>
          </div>
        </div>
      </div>
    );
  }
);