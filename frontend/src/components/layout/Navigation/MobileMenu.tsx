import React, { forwardRef } from 'react';

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

export const MobileMenu = forwardRef<HTMLDivElement, MobileMenuProps>(
  function MobileMenu({ isOpen, onClose, children }, ref) {
    return (
      <div
        className={`
          fixed inset-0 z-40
          transition-all duration-300 ease-in-out
          ${isOpen ? 'visible opacity-100' : 'invisible opacity-0 pointer-events-none'}
        `}
        aria-hidden={!isOpen}
      >
        {/* Backdrop */}
        <div
          onClick={onClose}
          className={`
            absolute inset-0
            bg-black/20 backdrop-blur-sm
            transition-opacity duration-300 cursor-pointer
            ${isOpen ? 'opacity-100' : 'opacity-0'}
          `}
        />

        {/* Menu Panel - 改为弹出层样式 */}
        <div
          ref={ref}
          className={`
            absolute left-4 top-16
            w-64 max-h-[calc(100vh-5rem)]
            rounded-lg
            backdrop-blur-md
            backdrop-opacity-70
            bg-white/5
            border border-white/10
            shadow-lg
            transition-all duration-300 ease-out
            ${isOpen ? 'opacity-100 scale-100 translate-y-0' : 'opacity-0 scale-95 -translate-y-2'}
            overflow-hidden
          `}
          style={{
            backdropFilter: 'blur(12px) opacity(0.7)'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* 菜单头部 */}
          <div className="px-4 py-3 bg-gradient-to-r from-blue-600/20 to-purple-600/20 border-b border-white/20">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
                  <svg className="w-4 h-4 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-semibold text-white">快速导航</h3>
                <p className="text-xs text-white/70">太空数据平台</p>
              </div>
            </div>
          </div>

          {/* 菜单内容 */}
          <div className="py-2 max-h-96 overflow-y-auto">
            {children}
          </div>
        </div>
      </div>
    );
  }
);